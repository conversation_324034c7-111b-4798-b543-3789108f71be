import { supabase } from '@/lib/supabase';
import { Team } from '@/types/teams';

export const checkIfTeamNameExists = async (
  tournamentId: string,
  name: string,
  excludeTeamId?: string
) => {
  let query = supabase
    .from('teams')
    .select('id')
    .eq('tournament_id', tournamentId)
    .ilike('name', name);

  // Exclude current team when editing
  if (excludeTeamId) {
    query = query.neq('id', excludeTeamId);
  }
  const { data, error } = await query;

  if (error) {
    console.error('Error checking team name:', error);
    return false;
  }

  return data.length > 0;
};

export const checkIfShortNameExists = async (
  tournamentId: string,
  shortName: string,
  excludeTeamId?: string
) => {
  let query = supabase
    .from('teams')
    .select('id')
    .eq('tournament_id', tournamentId)
    .ilike('short_name', shortName);

  // Exclude current team when editing
  if (excludeTeamId) {
    query = query.neq('id', excludeTeamId);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error checking short name:', error);
    return false;
  }

  return data.length > 0;
};

export const createTeam = async ({
  tournamentId,
  team,
}: {
  tournamentId: string;
  team: Team;
}) => {
  const { data: createdTeam, error } = await supabase
    .from('teams')
    .insert([
      {
        tournament_id: tournamentId,
        team,
      },
    ])
    .select()
    .single();

  if (error) {
    return { success: false, error: error.message };
  }

  return { success: true, player: createdTeam };
};
