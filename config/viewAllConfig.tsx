import { fetchUserTournaments } from '@/services/tournamentService';
import { fetchPlayers } from '@/services/playerService';
import EventCard from '@/components/k-components/EventCard';
import { PlayerListCard } from '@/pages/Players/components/PlayerListCard';
import { getSportLabel } from '@/utils/sports-utils';
import SCREENS from '@/constants/Screens';

export interface ViewAllConfigItem<T> {
  title: string;
  fetchData: (
    query: string,
    limit?: number,
    extraParams?: Record<string, any>
  ) => Promise<{
    data: T[];
    totalCount?: number;
  }>;
  renderCard: (item: T) => JSX.Element;
}

export interface ViewAllConfigMap {
  [key: string]: ViewAllConfigItem<any>;
}

export const VIEW_ALL_CONFIG: ViewAllConfigMap = {
  tournament: {
    title: 'Your Tournaments',
    fetchData: async (query, limit = 10, extraParams = {}) => {
      const { page = 1 } = extraParams;

      const res = await fetchUserTournaments({
        search: query,
        limit,
        page,
      });

      return {
        data: res.data || [],
        totalCount: res.count ?? 0,
      };
    },
    renderCard: (item) => (
      <EventCard
        key={item.id}
        name={item.name}
        logo={item.logo_url}
        venue={item.venue}
        type={getSportLabel(item.sport_type)}
        status={item.status}
        routeOnPress={{
          pathname: SCREENS.TOURNAMENT_VIEW,
          params: {
            'tournament-id': item.id,
          },
        }}
      />
    ),
  },
  players: {
    title: 'Players',
    fetchData: async (query, limit = 10, extraParams = {}) => {
      const { tournamentId, page = 1 } = extraParams;
      const res = await fetchPlayers({
        tournamentId,
        search: query,
        page,
        limit,
      });

      return {
        data: res.players,
        totalCount: res.count,
      };
    },
    renderCard: (item) => <PlayerListCard key={item.id} player={item} />,
  },
};
