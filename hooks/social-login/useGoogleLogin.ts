import { useState } from 'react';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { supabase } from '@/lib/supabase';
import { toast } from '@/toast/toast';

/**
 * Google Sign-In Hook following Supabase documentation exactly
 * Based on: https://supabase.com/docs/guides/auth/social-login/auth-google
 */

export interface GoogleLoginResult {
  success: boolean;
  error?: string;
}

export function useGoogleLogin() {
  const [loading, setLoading] = useState(false);

  const signInWithGoogle = async (): Promise<GoogleLoginResult> => {
    setLoading(true);

    try {
      await GoogleSignin.hasPlayServices();
      const userInfo = await GoogleSignin.signIn();

      if (userInfo.data?.idToken) {
        const { data, error } = await supabase.auth.signInWithIdToken({
          provider: 'google',
          token: userInfo.data.idToken,
        });

        if (error) {
          console.error('Supabase auth error:', error);
          return { success: false, error: 'Authentication failed' };
        }

        if (data.user) {
          toast.success('Successfully signed in with Google!');
          return { success: true };
        }
      } else {
        throw new Error('no ID token present!');
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // user cancelled the login flow
        return { success: false, error: 'Sign-in cancelled' };
      } else if (error.code === statusCodes.IN_PROGRESS) {
        // operation (e.g. sign in) is in progress already
        return { success: false, error: 'Sign-in already in progress' };
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        // play services not available or outdated
        return { success: false, error: 'Google Play Services not available' };
      } else {
        // some other error happened
        console.error('Google Sign-In error:', error);
        return { success: false, error: 'Something went wrong' };
      }
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    signInWithGoogle,
  };
}
