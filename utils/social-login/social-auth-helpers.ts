import { configureGoogleSignIn, isGoogleConfigured } from './google-config';
import { isAppleSignInSupported } from './apple-config';

/**
 * Social Login Helpers for Supabase
 * Simplified configuration following Supabase documentation
 */

export interface SocialLoginProvider {
  id: 'google' | 'apple';
  name: string;
  isAvailable: boolean;
  isConfigured: boolean;
}

export const initializeSocialLogin = () => {
  try {
    // Configure Google Sign-In following Supabase docs
    if (isGoogleConfigured()) {
      configureGoogleSignIn();
      console.log('Google Sign-In configured successfully');
    } else {
      console.warn(
        'Google Sign-In not configured - missing EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID'
      );
    }
  } catch (error) {
    console.error('Failed to initialize Google Sign-In:', error);
  }
};

export const getAvailableSocialProviders = (): SocialLoginProvider[] => {
  try {
    const providers: SocialLoginProvider[] = [
      {
        id: 'google',
        name: 'Google',
        isAvailable: true, // Google is available on all platforms
        isConfigured: isGoogleConfigured(),
      },
      {
        id: 'apple',
        name: 'Apple',
        isAvailable: isAppleSignInSupported(),
        isConfigured: isAppleSignInSupported(), // Apple doesn't need additional config
      },
    ];

    return providers.filter(
      (provider) => provider.isAvailable && provider.isConfigured
    );
  } catch (error) {
    console.warn('Failed to get available social providers:', error);
    return [];
  }
};

export const getSocialLoginErrorMessage = (error: string): string => {
  const errorMap: Record<string, string> = {
    'Sign-in cancelled': 'Sign-in was cancelled',
    'Sign-in already in progress': 'Sign-in is already in progress',
    'Google Play Services not available':
      'Google Play Services is not available on this device',
    'Apple Sign In is not available on this device':
      'Apple Sign In is not available on this device',
    'Failed to get Google ID token': 'Failed to authenticate with Google',
    'Failed to get Apple ID token': 'Failed to authenticate with Apple',
    'Invalid response from Apple': 'Invalid response from Apple',
    'Apple Sign In request failed': 'Apple Sign In request failed',
    'Google Sign-In failed': 'Google Sign-In failed',
    'Apple Sign In failed': 'Apple Sign In failed',
  };

  return errorMap[error] || error || 'Something went wrong';
};
