# Google Sign-In Setup

Simple Google Sign-In button following Supabase documentation exactly.

## Setup Steps

1. **Configure Google Cloud Console:**

   - Create OAuth 2.0 credentials
   - Get your web client ID

2. **Configure Supabase:**

   - Enable Google provider
   - Add your web client ID and secret

3. **Update your app:**

   ```bash
   # Copy .env.example to .env
   cp .env.example .env

   # Edit .env and add your web client ID
   EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
   ```

4. **Use the component:**

   ```tsx
   import GoogleSignInButtonComponent from '@/components/GoogleSignInButton';

   // In your component
   <GoogleSignInButtonComponent />;
   ```

## What's Included

- ✅ Simple Google Sign-In button component
- ✅ Follows Supabase documentation exactly
- ✅ Handles all error cases
- ✅ Uses environment variables for configuration

## Testing

1. Start your app: `npm run ios` or `npm run android`
2. Navigate to login/signup screen
3. Tap the Google Sign-In button
4. Complete authentication flow

That's it! Simple Google Sign-In following Supabase docs exactly.

## Apple Sign In Setup

### 1. Apple Developer Account Setup

1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Navigate to Certificates, Identifiers & Profiles
3. Create or update your App ID to include "Sign In with Apple" capability
4. Create a Service ID for web authentication
5. Create a Key for client authentication

### 2. Configure Supabase

1. Go to your Supabase dashboard
2. Navigate to Authentication > Providers
3. Enable Apple provider
4. Add your Apple OAuth credentials:
   - **Client ID**: Your Service ID
   - **Client Secret**: Generate using your private key

### 3. iOS Configuration

Apple Sign In is automatically available on iOS devices with iOS 13+ when using `expo-apple-authentication`.

## Testing

### Development Testing

1. **Google Sign-In**: Works in Expo Go and development builds
2. **Apple Sign In**: Only works on physical iOS devices with iOS 13+

### Production Testing

Both providers work in production builds on their respective platforms.

## Troubleshooting

### Common Issues

1. **Google Sign-In not working**: Check that your web client ID is correct
2. **Apple Sign In not available**: Ensure you're testing on iOS 13+ device
3. **Supabase errors**: Verify your provider configuration in Supabase dashboard

### Debug Steps

1. Check console logs for detailed error messages
2. Verify your OAuth client IDs and secrets
3. Ensure your app's bundle ID matches your OAuth configuration
4. Test with a fresh app installation

## Security Notes

1. Never commit OAuth secrets to version control
2. Use environment variables for sensitive configuration
3. Regularly rotate your OAuth credentials
4. Monitor authentication logs in Supabase dashboard

## Next Steps

After configuration:

1. Test both providers on their respective platforms
2. Implement proper error handling for edge cases
3. Add analytics tracking for social login usage
4. Consider implementing account linking for existing users
