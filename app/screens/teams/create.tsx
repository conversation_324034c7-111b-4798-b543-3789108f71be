import { useRouter, useLocalSearchParams } from 'expo-router';
import { NavLayout } from '@/components/NavLayout';
import TeamForm from '@/pages/Teams/TeamForm';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { useTournamentById } from '@/hooks/useTournamentById';
import { type TeamFormData } from '@/types/teams';

export default function CreateTeamModal() {
  const router = useRouter();
  const { 'tournament-id': tournamentId } = useLocalSearchParams();
  const { tournament, loading } = useTournamentById(tournamentId as string);

  const handleSubmit = async (formData: TeamFormData) => {
    // API call to create team
    console.log('Team Form Values:', formData);

    router.back();
  };

  return (
    <NavLayout title="Create Team">
      {loading ? (
        <FullscreenLoader />
      ) : tournament ? (
        <TeamForm
          onSubmit={handleSubmit}
          submitButtonText="Create Team"
          isLoading={false}
          tournamentId={tournamentId as string}
        />
      ) : null}
    </NavLayout>
  );
}
