import { Stack } from 'expo-router';
import { Animated, useEffect } from 'react-native';
import { GluestackUIProvider } from '@/components/ui/gluestack-ui-provider';
import * as SplashScreen from 'expo-splash-screen';
import AnimatedSplash from '@/components/SplashScreen';
import { useSplashAnimation } from '@/hooks/useSplashAnimation';
import { useLoadFonts } from '@/hooks/useLoadFonts';
import { ToastProvider } from '@gluestack-ui/toast';
import { SplashProvider } from '@/context/SplashContext';
import { RecoilRoot } from 'recoil';
import { useAuthUser } from '@/hooks/useAuthUser';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';
import { initializeSocialLogin } from '@/utils/social-login/social-auth-helpers';
import '../global.css';

export { ErrorBoundary } from 'expo-router';

SplashScreen.preventAutoHideAsync();

function AppLayout() {
  const { fontsLoaded } = useLoadFonts();
  const { splashDone, fadeAnim, backgroundColor, handleSplashAnimationEnd } =
    useSplashAnimation();
  const { loading: authLoading } = useAuthUser();
  if (!fontsLoaded || !splashDone) {
    return <AnimatedSplash onAnimationEnd={handleSplashAnimationEnd} />;
  }

  if (authLoading) {
    return <FullscreenLoader />;
  }

  return (
    <Animated.View style={{ flex: 1, backgroundColor }}>
      <Animated.View style={{ flex: 1, opacity: fadeAnim }}>
        <RecoilRoot>
          <GluestackUIProvider
            mode="light"
            // @ts-ignore
            config={{
              theme: {
                fontConfig: {
                  body: 'Urbanist_400Regular',
                  heading: 'Urbanist_600SemiBold',
                },
              },
            }}
          >
            <Stack
              screenOptions={{
                animation: 'slide_from_right',
                headerShown: false,
              }}
            />
          </GluestackUIProvider>
        </RecoilRoot>
      </Animated.View>
    </Animated.View>
  );
}

export default function RootLayout() {
  return (
    <SplashProvider>
      <ToastProvider>
        <AppLayout />
      </ToastProvider>
    </SplashProvider>
  );
}
