import React from 'react';
import { GoogleSigninButton } from '@react-native-google-signin/google-signin';
import { useGoogleLogin } from '@/hooks/social-login/useGoogleLogin';

/**
 * Google Sign-In Button following Supabase documentation exactly
 * Based on: https://supabase.com/docs/guides/auth/social-login/auth-google
 */

interface GoogleSignInButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  disabled?: boolean;
}

export function GoogleSignInButton({
  onSuccess,
  onError,
  disabled = false,
}: GoogleSignInButtonProps) {
  const { loading, signInWithGoogle } = useGoogleLogin();

  const handleGoogleSignIn = async () => {
    const result = await signInWithGoogle();

    if (result.success) {
      onSuccess?.();
    } else if (result.error && result.error !== 'Sign-in cancelled') {
      onError?.(result.error);
    }
  };

  return (
    <GoogleSigninButton
      size={GoogleSigninButton.Size.Wide}
      color={GoogleSigninButton.Color.Dark}
      onPress={handleGoogleSignIn}
      disabled={disabled || loading}
      style={{ width: '100%', height: 48 }}
    />
  );
}
