import React, { useState, useCallback, useMemo } from 'react';
import {
  Actionsheet,
  ActionsheetBackdrop,
  ActionsheetContent,
  ActionsheetDragIndicatorWrapper,
  ActionsheetDragIndicator,
  ActionsheetItem,
  ActionsheetItemText,
  ActionsheetFlatList,
} from '@/components/ui/actionsheet';
import {
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
} from '@/components/ui/checkbox';
import { CheckIcon } from '@/components/ui/icon';
import { Spinner } from '@/components/ui/spinner';
import { VStack } from '@/components/ui/vstack';
import SearchBar from './SearchBar';
import { Pressable } from 'react-native';

export interface Option {
  label: string;
  value: string;
  [key: string]: any;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSelect: (selected: Option[] | Option | null) => void;
  selectedValues?: Option[];
  selectedValue?: Option;
  multiple?: boolean;
  maxSelect?: number;
  options: Option[];
  hasMore: boolean;
  loadMore: (searchQuery: string, page: number) => Promise<void>;
  placeholder?: string;
  renderOption?: (option: Option, isSelected: boolean) => React.ReactNode;
  onSearchChange?: (query: string) => void;
}

export const AsyncSelectWithSearch = ({
  isOpen,
  onClose,
  onSelect,
  selectedValues = [],
  selectedValue,
  multiple = false,
  maxSelect = 5,
  options,
  hasMore,
  loadMore,
  placeholder = 'Search...',
  renderOption,
  onSearchChange,
}: Props) => {
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);

  const localSelected = useMemo(() => {
    if (multiple) return selectedValues;
    if (selectedValue) return [selectedValue];
    return [];
  }, [multiple, selectedValues, selectedValue]);

  const handleSearchChange = useCallback(
    (query: string) => {
      setSearchQuery(query);
      setPage(1);
      onSearchChange?.(query);
    },
    [onSearchChange]
  );

  const toggleSelect = useCallback(
    (option: Option) => {
      if (!multiple) {
        onSelect(option);
        onClose();
        return;
      }

      const exists = localSelected.some((o) => o.value === option.value);
      const updated = exists
        ? localSelected.filter((o) => o.value !== option.value)
        : [...localSelected, option];

      if (updated.length <= maxSelect) {
        onSelect(updated);
      }
    },
    [multiple, localSelected, maxSelect, onSelect, onClose]
  );

  const renderItem = useCallback(
    ({ item }: { item: Option }) => {
      const isSelected = localSelected.some((o) => o.value === item.value);
      const isDisabled =
        multiple && !isSelected && localSelected.length >= maxSelect;

      return (
        <ActionsheetItem
          key={item.value}
          onPress={() => toggleSelect(item)}
          isDisabled={isDisabled}
        >
          <Pressable className="flex-row items-center gap-4">
            <Checkbox
              onChange={() => toggleSelect(item)}
              size="md"
              value={item.value}
              isChecked={isSelected}
            >
              <CheckboxIndicator>
                <CheckboxIcon as={CheckIcon} />
              </CheckboxIndicator>
            </Checkbox>
            {renderOption ? (
              renderOption(item, isSelected)
            ) : (
              <Pressable className="flex-1" onPress={() => toggleSelect(item)}>
                <ActionsheetItemText
                  className={` text-base py-2 ${
                    isSelected ? 'font-urbanistBold' : 'font-urbanistMedium'
                  }`}
                >
                  {item.label}
                </ActionsheetItemText>
              </Pressable>
            )}
          </Pressable>
        </ActionsheetItem>
      );
    },
    [localSelected, multiple, maxSelect, toggleSelect, renderOption]
  );

  const handleEndReached = useCallback(async () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setLoading(true);
      try {
        await loadMore(searchQuery, nextPage);
        setPage(nextPage);
      } catch (error) {
        console.error('Error loading more items:', error);
      } finally {
        setLoading(false);
      }
    }
  }, [loading, hasMore, page, searchQuery, loadMore]);

  const keyExtractor = useCallback((item: any) => item.value, []);

  return (
    <Actionsheet isOpen={isOpen} onClose={onClose} snapPoints={[85]}>
      <ActionsheetBackdrop />
      <ActionsheetContent className="max-h-[85vh]">
        <ActionsheetDragIndicatorWrapper>
          <ActionsheetDragIndicator />
        </ActionsheetDragIndicatorWrapper>

        <VStack space="sm" className="w-full mt-2 flex-1">
          <SearchBar
            value={searchQuery}
            onDebouncedChange={handleSearchChange}
            placeholder={placeholder}
          />

          <ActionsheetFlatList
            data={options}
            renderItem={renderItem as any}
            showsVerticalScrollIndicator={false}
            keyExtractor={keyExtractor}
            onEndReached={handleEndReached}
            onEndReachedThreshold={0.4}
            ListFooterComponent={loading ? <Spinner className="my-2" /> : null}
            className="flex-1"
            contentContainerClassName="pb-6"
            style={{ flexGrow: 1 }}
          />
        </VStack>
      </ActionsheetContent>
    </Actionsheet>
  );
};
